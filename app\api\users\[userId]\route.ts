import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { doc, getDoc, updateDoc, deleteDoc, collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { getUserWithSubcollections, listUserAudio } from '@/lib/firebase-service'

// GET /api/users/[userId] - Get detailed user information
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params

    // Users can view their own profile, admins can view any profile
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get user with all subcollections
    const userWithSubcollections = await getUserWithSubcollections(userId, [
      'profile', 'statistics', 'preferences', 'security'
    ])

    if (!userWithSubcollections) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get user's audio recordings for additional statistics
    const userAudio = await listUserAudio(userId, { limit: 1000 })
    
    // Calculate detailed statistics
    const audioStats = {
      total: userAudio.length,
      approved: userAudio.filter(a => a.review?.status?.action === 'approved').length,
      pending: userAudio.filter(a => (a.review?.status?.action || 'pending') === 'pending').length,
      rejected: userAudio.filter(a => a.review?.status?.action === 'rejected').length,
      totalDuration: userAudio.reduce((acc, a) => acc + (a.duration || 0), 0),
      averageDuration: userAudio.length > 0 ? userAudio.reduce((acc, a) => acc + (a.duration || 0), 0) / userAudio.length : 0
    }

    // Get recent activity (last 10 audio uploads)
    const recentActivity = userAudio
      .sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime())
      .slice(0, 10)
      .map(audio => ({
        id: audio.id,
        type: 'audio_upload',
        title: audio.transcriptions?.primary?.content || audio.title || 'Audio Upload',
        status: audio.review?.status?.action || 'pending',
        created_at: audio.created_at,
        duration: audio.duration
      }))

    // If admin is viewing, include sensitive information
    const responseData = {
      ...userWithSubcollections,
      audioStats,
      recentActivity,
      // Only include sensitive data for admins or the user themselves
      ...(currentUserData.role === 'admin' || currentUserId === userId ? {
        loginHistory: [], // TODO: Implement login history tracking
        securityEvents: [] // TODO: Implement security event tracking
      } : {})
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/users/[userId] - Update user information
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params
    const updates = await request.json()

    // Users can update their own profile, admins can update any profile
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Validate updates based on user role
    const allowedUpdatesForUser = ['name', 'username']
    const allowedUpdatesForAdmin = ['name', 'username', 'role', 'isDisabled', 'email_verified']

    const allowedUpdates = currentUserData.role === 'admin' ? allowedUpdatesForAdmin : allowedUpdatesForUser
    const updateKeys = Object.keys(updates)
    const invalidKeys = updateKeys.filter(key => !allowedUpdates.includes(key))

    if (invalidKeys.length > 0) {
      return NextResponse.json({
        error: `Invalid update fields: ${invalidKeys.join(', ')}`
      }, { status: 400 })
    }

    // Prevent users from changing their own role
    if (currentUserId === userId && updates.role) {
      return NextResponse.json({
        error: 'Cannot change your own role'
      }, { status: 400 })
    }

    // Update main user document
    const userRef = doc(db, 'users', userId)
    await updateDoc(userRef, {
      ...updates,
      updated_at: new Date().toISOString()
    })

    // Get updated user data
    const updatedUser = await getUserWithSubcollections(userId, [
      'profile', 'statistics', 'preferences'
    ])

    return NextResponse.json({
      message: 'User updated successfully',
      user: updatedUser
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/users/[userId] - Delete user (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    // Only admins can delete users
    if (currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { userId } = await params

    // Prevent admin from deleting themselves
    if (currentUserId === userId) {
      return NextResponse.json({
        error: 'Cannot delete your own account'
      }, { status: 400 })
    }

    // Check if user exists
    const userRef = doc(db, 'users', userId)
    const userDoc = await getDoc(userRef)
    
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // TODO: Implement soft delete instead of hard delete
    // For now, just mark as disabled
    await updateDoc(userRef, {
      isDisabled: true,
      deleted_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })

    return NextResponse.json({
      message: 'User deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
