rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Users collection - users can read/write their own data, admins can read all
    match /users/{userId} {
      // Allow users to create their own document during signup
      allow create: if request.auth != null && request.auth.uid == userId;
      // Allow users to read/write their own data
      allow read, update: if request.auth != null && request.auth.uid == userId;
      // Allow admins to read all user data
      allow read: if isAdmin();
      // Allow admins to update user data (for role changes, etc.)
      allow update: if isAdmin();
      // TEMPORARY: Allow reading for authentication (username/email lookup)
      // This will be removed after we fix the login flow
      allow read: if request.auth == null;

      // User subcollections - users can access their own, admins can access all
      match /activity_logs/{activityId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        allow read: if isAdmin();
      }

      match /security_events/{eventId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        allow read, write: if isAdmin();
      }

      match /sessions/{sessionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        allow read: if isAdmin();
      }
    }
    
    // Audio collection - users can read/write their own, admins can read all
    match /audio/{audioId} {
      allow create: if request.auth != null && request.resource.data.user_id == request.auth.uid;
      allow read, update, delete: if request.auth != null && resource.data.user_id == request.auth.uid;
      allow read, write: if isAdmin();
    }
    
    // Review collection - only admins can write, users can read their own
    match /review/{reviewId} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
    }
    
    // Training collections - admin only
    match /training/{document=**} {
      allow read, write: if isAdmin();
    }

    // Models collection - admin only
    match /models/{document=**} {
      allow read, write: if isAdmin();
    }

    // System collections - admin only
    match /system_settings/{document=**} {
      allow read, write: if isAdmin();
    }

    match /system_logs/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // Legacy collections (will be removed after migration)
    // These rules are kept temporarily for backward compatibility
    match /activity_logs/{activityId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }

    match /security_events/{eventId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }

    match /user_sessions/{sessionId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
      allow read, write: if isAdmin();
    }
  }
}
