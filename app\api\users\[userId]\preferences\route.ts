import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/firebase-admin'

interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: {
    email: boolean
    push: boolean
    training_updates: boolean
    review_notifications: boolean
    system_announcements: boolean
    weekly_summary: boolean
  }
  privacy: {
    profile_public: boolean
    stats_public: boolean
    allow_contact: boolean
    show_activity: boolean
  }
  accessibility: {
    high_contrast: boolean
    large_text: boolean
    reduced_motion: boolean
    screen_reader: boolean
  }
  audio: {
    auto_play: boolean
    default_volume: number
    quality_preference: 'low' | 'medium' | 'high'
    download_format: 'mp3' | 'wav' | 'original'
  }
  interface: {
    compact_mode: boolean
    show_tooltips: boolean
    default_page_size: number
    auto_save: boolean
  }
  created_at?: string
  updated_at?: string
}

const defaultPreferences: UserPreferences = {
  theme: 'system',
  language: 'en',
  notifications: {
    email: true,
    push: true,
    training_updates: true,
    review_notifications: true,
    system_announcements: true,
    weekly_summary: false
  },
  privacy: {
    profile_public: false,
    stats_public: false,
    allow_contact: true,
    show_activity: false
  },
  accessibility: {
    high_contrast: false,
    large_text: false,
    reduced_motion: false,
    screen_reader: false
  },
  audio: {
    auto_play: false,
    default_volume: 0.8,
    quality_preference: 'medium',
    download_format: 'mp3'
  },
  interface: {
    compact_mode: false,
    show_tooltips: true,
    default_page_size: 20,
    auto_save: true
  }
}

// GET /api/users/[userId]/preferences - Get user preferences
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {


    const { userId } = await params

    // Get preferences from subcollection
    const preferencesDoc = await db.collection('users').doc(userId).collection('preferences').doc('settings').get()

    let preferences: UserPreferences
    if (preferencesDoc.exists) {
      preferences = { ...defaultPreferences, ...preferencesDoc.data() } as UserPreferences
    } else {
      // Create default preferences if they don't exist
      preferences = {
        ...defaultPreferences,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      await db.collection('users').doc(userId).collection('preferences').doc('settings').set(preferences)
    }

    return NextResponse.json(preferences)
  } catch (error) {
    console.error('Error fetching user preferences:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/users/[userId]/preferences - Update user preferences
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    const { userId } = await params
    const updates = await request.json()

    // Validate updates structure
    const allowedTopLevelKeys = ['theme', 'language', 'notifications', 'privacy', 'accessibility', 'audio', 'interface']
    const updateKeys = Object.keys(updates)
    const invalidKeys = updateKeys.filter(key => !allowedTopLevelKeys.includes(key))
    
    if (invalidKeys.length > 0) {
      return NextResponse.json({ 
        error: `Invalid preference fields: ${invalidKeys.join(', ')}` 
      }, { status: 400 })
    }

    // Get current preferences
    const preferencesDoc = await db.collection('users').doc(userId).collection('preferences').doc('settings').get()

    let currentPreferences: UserPreferences
    if (preferencesDoc.exists) {
      currentPreferences = preferencesDoc.data() as UserPreferences
    } else {
      currentPreferences = defaultPreferences
    }

    // Merge updates with current preferences
    const updatedPreferences = {
      ...currentPreferences,
      ...updates,
      updated_at: new Date().toISOString()
    }

    // Validate specific preference values
    if (updates.theme && !['light', 'dark', 'system'].includes(updates.theme)) {
      return NextResponse.json({ 
        error: 'Invalid theme value. Must be light, dark, or system' 
      }, { status: 400 })
    }

    if (updates.audio?.quality_preference && !['low', 'medium', 'high'].includes(updates.audio.quality_preference)) {
      return NextResponse.json({ 
        error: 'Invalid audio quality preference. Must be low, medium, or high' 
      }, { status: 400 })
    }

    if (updates.audio?.download_format && !['mp3', 'wav', 'original'].includes(updates.audio.download_format)) {
      return NextResponse.json({ 
        error: 'Invalid download format. Must be mp3, wav, or original' 
      }, { status: 400 })
    }

    if (updates.audio?.default_volume && (updates.audio.default_volume < 0 || updates.audio.default_volume > 1)) {
      return NextResponse.json({ 
        error: 'Invalid volume level. Must be between 0 and 1' 
      }, { status: 400 })
    }

    // Update preferences
    await db.collection('users').doc(userId).collection('preferences').doc('settings').set(updatedPreferences)

    return NextResponse.json({
      message: 'Preferences updated successfully',
      preferences: updatedPreferences
    })
  } catch (error) {
    console.error('Error updating user preferences:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT /api/users/[userId]/preferences - Reset preferences to default
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params

    // Reset to default preferences
    const resetPreferences = {
      ...defaultPreferences,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    await db.collection('users').doc(userId).collection('preferences').doc('settings').set(resetPreferences)

    return NextResponse.json({
      message: 'Preferences reset to default successfully',
      preferences: resetPreferences
    })
  } catch (error) {
    console.error('Error resetting user preferences:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
